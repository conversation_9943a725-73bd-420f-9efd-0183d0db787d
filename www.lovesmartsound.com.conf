# HTTP -> HTTPS 强制跳转
server {
    listen 80;
    server_name www.lovesmartsound.com;
    return 301 https://$host$request_uri;
}

# HTTPS 主配置
server {
    listen 443 ssl;
    http2 on;
    server_name www.lovesmartsound.com;

    # SSL 证书配置
    ssl_certificate           /etc/nginx/conf/key/lovesmartsound.com/fullchain.pem;
    ssl_certificate_key       /etc/nginx/conf/key/lovesmartsound.com/privkey.pem;
    ssl_trusted_certificate   /etc/nginx/conf/key/lovesmartsound.com/chain.pem;

    ssl_session_timeout       10m;
    ssl_protocols             TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE+AESGCM:ECDHE+CHACHA20:HIGH:!aNULL:!MD5:!3DES';
    ssl_prefer_server_ciphers on;

    # 安全 HTTP 头
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options nosniff;

    # 前端 SPA 入口
    location / {
        root /data/project/official-website-fe/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存优化
    location ~* \.(?:ico|css|js|gif|jpe?g|png|woff2?|ttf|svg)$ {
        root /data/project/official-website-fe/dist;
        expires 7d;
        access_log off;
    }

    # Google 域名验证文件
    location = /google2fb32b8d107bc88e.html {
        root /etc/nginx/conf/html;
        try_files $uri =404;
    }

    # 广告联盟验证
    # location /ads.txt {
    #     alias /etc/nginx/conf/html/ads.txt;
    # }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
